#!/usr/bin/env python3
"""
Test script to verify that the dtype fixes resolve the array operation errors.
"""

import sys
import os
import pandas as pd
import numpy as np
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, 'src')

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_data_loading():
    """Test data loading and basic preprocessing."""
    logger.info("Testing data loading...")
    
    # Load a sample dataset
    data_file = Path("data/processed/entso_e_solar.csv")
    if not data_file.exists():
        logger.error(f"Data file not found: {data_file}")
        return False
    
    data = pd.read_csv(data_file)
    logger.info(f"Loaded data shape: {data.shape}")
    logger.info(f"Data columns: {list(data.columns)}")
    logger.info(f"Data dtypes: {data.dtypes.to_dict()}")
    
    # Check for any non-numeric values in numeric columns
    numeric_cols = ['energy_generation', 'hour', 'day_of_week', 'month', 'day_of_year', 
                   'cloud_cover', 'temperature', 'humidity']
    
    for col in numeric_cols:
        if col in data.columns:
            non_numeric = data[col].apply(lambda x: not isinstance(x, (int, float, np.integer, np.floating)))
            if non_numeric.any():
                logger.warning(f"Column {col} contains non-numeric values: {data[col][non_numeric].head()}")
            else:
                logger.info(f"Column {col} is properly numeric")
    
    return True

def test_neutrosophic_transformer():
    """Test the neutrosophic transformer with sample data."""
    logger.info("Testing neutrosophic transformer...")

    try:
        from src.neutrosophic.neutrosophic_transformer import NeutrosophicTransformer
        
        # Create sample data
        n_samples = 100
        n_clusters = 5
        
        # Create proper integer labels
        kmeans_labels = np.random.randint(0, n_clusters, size=n_samples)
        
        # Create proper float membership matrix
        fcm_memberships = np.random.dirichlet(np.ones(n_clusters), size=n_samples)
        
        logger.info(f"K-means labels dtype: {kmeans_labels.dtype}, shape: {kmeans_labels.shape}")
        logger.info(f"FCM memberships dtype: {fcm_memberships.dtype}, shape: {fcm_memberships.shape}")
        
        # Test transformer
        transformer = NeutrosophicTransformer()
        components = transformer.transform(kmeans_labels, fcm_memberships)
        
        logger.info(f"Truth shape: {components.truth.shape}, dtype: {components.truth.dtype}")
        logger.info(f"Indeterminacy shape: {components.indeterminacy.shape}, dtype: {components.indeterminacy.dtype}")
        logger.info(f"Falsity shape: {components.falsity.shape}, dtype: {components.falsity.dtype}")
        
        # Test enriched features creation
        original_features = np.random.randn(n_samples, 1).astype(np.float64)
        integrated_features = np.random.randn(n_samples, 2 * n_clusters).astype(np.float64)
        
        enriched = transformer.create_enriched_features(
            original_features, integrated_features, components
        )
        
        logger.info(f"Enriched features shape: {enriched.shape}, dtype: {enriched.dtype}")
        logger.info("Neutrosophic transformer test passed!")
        return True
        
    except Exception as e:
        logger.error(f"Neutrosophic transformer test failed: {e}")
        return False

def test_dual_clustering():
    """Test dual clustering with sample data."""
    logger.info("Testing dual clustering...")

    try:
        from src.clustering.dual_clusterer import DualClusterer
        
        # Create sample data
        n_samples = 100
        n_features = 1
        X = np.random.randn(n_samples, n_features).astype(np.float64)
        
        logger.info(f"Input data shape: {X.shape}, dtype: {X.dtype}")
        
        # Test dual clusterer
        clusterer = DualClusterer(n_clusters=5)
        clusterer.fit(X)
        
        # Get cluster assignments
        kmeans_labels, fcm_memberships = clusterer.get_cluster_assignments()
        
        logger.info(f"K-means labels dtype: {kmeans_labels.dtype}, shape: {kmeans_labels.shape}")
        logger.info(f"FCM memberships dtype: {fcm_memberships.dtype}, shape: {fcm_memberships.shape}")
        
        # Get integrated features
        integrated_features = clusterer.get_integrated_features()
        logger.info(f"Integrated features shape: {integrated_features.shape}, dtype: {integrated_features.dtype}")
        
        logger.info("Dual clustering test passed!")
        return True
        
    except Exception as e:
        logger.error(f"Dual clustering test failed: {e}")
        return False

def test_framework_integration():
    """Test the complete framework integration."""
    logger.info("Testing framework integration...")

    try:
        from src.framework.forecasting_framework import NeutrosophicForecastingFramework
        
        # Load sample data
        data_file = Path("data/processed/entso_e_solar.csv")
        data = pd.read_csv(data_file)
        
        # Prepare data (use only first 200 samples for quick test)
        data = data.head(200)
        data['timestamp'] = pd.to_datetime(data['timestamp'])
        data = data.set_index('timestamp')
        
        # Ensure energy_generation is numeric
        data['energy_generation'] = pd.to_numeric(data['energy_generation'], errors='coerce')
        data = data.dropna()
        
        logger.info(f"Test data shape: {data.shape}")
        logger.info(f"Energy generation dtype: {data['energy_generation'].dtype}")
        
        # Test framework
        config = {
            'clustering': {'n_clusters': 3},
            'forecasting': {'n_estimators': 10}  # Small for quick test
        }
        
        framework = NeutrosophicForecastingFramework(config=config)
        framework.fit(data)
        
        # Test prediction
        test_input = data.tail(1)
        predictions = framework.predict(test_input, horizon=5)
        
        logger.info(f"Predictions shape: {len(predictions['predictions'])}")
        logger.info("Framework integration test passed!")
        return True
        
    except Exception as e:
        logger.error(f"Framework integration test failed: {e}")
        return False

def main():
    """Run all tests."""
    logger.info("Starting dtype fixes validation tests...")
    
    tests = [
        ("Data Loading", test_data_loading),
        ("Neutrosophic Transformer", test_neutrosophic_transformer),
        ("Dual Clustering", test_dual_clustering),
        ("Framework Integration", test_framework_integration)
    ]
    
    results = {}
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running {test_name} test...")
        logger.info(f"{'='*50}")
        
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"{test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = 0
    for test_name, result in results.items():
        status = "PASSED" if result else "FAILED"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        logger.info("All tests passed! The dtype fixes appear to be working.")
        return 0
    else:
        logger.error("Some tests failed. The dtype issues may not be fully resolved.")
        return 1

if __name__ == "__main__":
    exit(main())
