=== Starting Comprehensive Evaluation ===
Job ID: 25543173
Host: n-62-18-13
Date: Thu Jul 10 11:29:40 CEST 2025
Working directory: /zhome/bb/9/101964/xiuli/dual_clustering
=== Installing dependencies ===
Requirement already satisfied: numpy>=1.21.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from -r requirements.txt (line 1)) (2.1.3)
Requirement already satisfied: pandas>=1.3.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from -r requirements.txt (line 2)) (2.2.3)
Requirement already satisfied: scikit-learn>=1.0.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from -r requirements.txt (line 3)) (1.6.1)
Requirement already satisfied: scikit-fuzzy>=0.4.2 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from -r requirements.txt (line 4)) (0.5.0)
Requirement already satisfied: scipy>=1.7.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from -r requirements.txt (line 5)) (1.15.3)
Requirement already satisfied: matplotlib>=3.4.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from -r requirements.txt (line 6)) (3.10.0)
Requirement already satisfied: seaborn>=0.11.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from -r requirements.txt (line 7)) (0.13.2)
Requirement already satisfied: plotly>=5.0.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from -r requirements.txt (line 8)) (5.24.1)
Requirement already satisfied: pyyaml>=6.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from -r requirements.txt (line 9)) (6.0.2)
Requirement already satisfied: requests>=2.25.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from -r requirements.txt (line 10)) (2.32.3)
Requirement already satisfied: tqdm>=4.62.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from -r requirements.txt (line 11)) (4.67.1)
Requirement already satisfied: joblib>=1.1.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from -r requirements.txt (line 12)) (1.4.2)
Requirement already satisfied: pytest>=6.2.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from -r requirements.txt (line 13)) (8.3.4)
Requirement already satisfied: pytest-cov>=3.0.0 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from -r requirements.txt (line 14)) (6.2.1)
Requirement already satisfied: black>=22.0.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from -r requirements.txt (line 15)) (24.10.0)
Requirement already satisfied: flake8>=4.0.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from -r requirements.txt (line 16)) (7.1.1)
Requirement already satisfied: mypy>=0.910 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from -r requirements.txt (line 17)) (1.14.1)
Requirement already satisfied: pre-commit>=2.15.0 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from -r requirements.txt (line 18)) (4.2.0)
Requirement already satisfied: jupyter>=1.0.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from -r requirements.txt (line 19)) (1.1.1)
Requirement already satisfied: ipykernel>=6.0.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from -r requirements.txt (line 20)) (6.29.5)
Requirement already satisfied: statsmodels>=0.13.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from -r requirements.txt (line 21)) (0.14.4)
Requirement already satisfied: lightgbm>=3.3.0 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from -r requirements.txt (line 22)) (4.6.0)
Requirement already satisfied: torch>=1.12.0 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from -r requirements.txt (line 23)) (2.7.1)
Requirement already satisfied: prophet>=1.1.0 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from -r requirements.txt (line 24)) (1.1.7)
Requirement already satisfied: optuna>=3.0.0 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from -r requirements.txt (line 25)) (4.4.0)
Requirement already satisfied: mlflow>=2.0.0 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from -r requirements.txt (line 26)) (3.1.1)
Requirement already satisfied: python-dateutil>=2.8.2 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from pandas>=1.3.0->-r requirements.txt (line 2)) (2.9.0.post0)
Requirement already satisfied: pytz>=2020.1 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from pandas>=1.3.0->-r requirements.txt (line 2)) (2024.1)
Requirement already satisfied: tzdata>=2022.7 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from pandas>=1.3.0->-r requirements.txt (line 2)) (2025.2)
Requirement already satisfied: threadpoolctl>=3.1.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from scikit-learn>=1.0.0->-r requirements.txt (line 3)) (3.5.0)
Requirement already satisfied: contourpy>=1.0.1 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from matplotlib>=3.4.0->-r requirements.txt (line 6)) (1.3.1)
Requirement already satisfied: cycler>=0.10 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from matplotlib>=3.4.0->-r requirements.txt (line 6)) (0.11.0)
Requirement already satisfied: fonttools>=4.22.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from matplotlib>=3.4.0->-r requirements.txt (line 6)) (4.55.3)
Requirement already satisfied: kiwisolver>=1.3.1 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from matplotlib>=3.4.0->-r requirements.txt (line 6)) (1.4.8)
Requirement already satisfied: packaging>=20.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from matplotlib>=3.4.0->-r requirements.txt (line 6)) (24.2)
Requirement already satisfied: pillow>=8 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from matplotlib>=3.4.0->-r requirements.txt (line 6)) (11.1.0)
Requirement already satisfied: pyparsing>=2.3.1 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from matplotlib>=3.4.0->-r requirements.txt (line 6)) (3.2.0)
Requirement already satisfied: tenacity>=6.2.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from plotly>=5.0.0->-r requirements.txt (line 8)) (9.0.0)
Requirement already satisfied: charset-normalizer<4,>=2 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from requests>=2.25.0->-r requirements.txt (line 10)) (3.3.2)
Requirement already satisfied: idna<4,>=2.5 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from requests>=2.25.0->-r requirements.txt (line 10)) (3.7)
Requirement already satisfied: urllib3<3,>=1.21.1 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from requests>=2.25.0->-r requirements.txt (line 10)) (2.3.0)
Requirement already satisfied: certifi>=2017.4.17 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from requests>=2.25.0->-r requirements.txt (line 10)) (2025.4.26)
Requirement already satisfied: iniconfig in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from pytest>=6.2.0->-r requirements.txt (line 13)) (1.1.1)
Requirement already satisfied: pluggy<2,>=1.5 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from pytest>=6.2.0->-r requirements.txt (line 13)) (1.5.0)
Requirement already satisfied: coverage>=7.5 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from coverage[toml]>=7.5->pytest-cov>=3.0.0->-r requirements.txt (line 14)) (7.9.2)
Requirement already satisfied: click>=8.0.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from black>=22.0.0->-r requirements.txt (line 15)) (8.1.8)
Requirement already satisfied: mypy-extensions>=0.4.3 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from black>=22.0.0->-r requirements.txt (line 15)) (1.0.0)
Requirement already satisfied: pathspec>=0.9.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from black>=22.0.0->-r requirements.txt (line 15)) (0.10.3)
Requirement already satisfied: platformdirs>=2 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from black>=22.0.0->-r requirements.txt (line 15)) (4.3.7)
Requirement already satisfied: mccabe<0.8.0,>=0.7.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from flake8>=4.0.0->-r requirements.txt (line 16)) (0.7.0)
Requirement already satisfied: pycodestyle<2.13.0,>=2.12.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from flake8>=4.0.0->-r requirements.txt (line 16)) (2.12.1)
Requirement already satisfied: pyflakes<3.3.0,>=3.2.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from flake8>=4.0.0->-r requirements.txt (line 16)) (3.2.0)
Requirement already satisfied: typing-extensions>=4.6.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from mypy>=0.910->-r requirements.txt (line 17)) (4.12.2)
Requirement already satisfied: cfgv>=2.0.0 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from pre-commit>=2.15.0->-r requirements.txt (line 18)) (3.4.0)
Requirement already satisfied: identify>=1.0.0 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from pre-commit>=2.15.0->-r requirements.txt (line 18)) (2.6.12)
Requirement already satisfied: nodeenv>=0.11.1 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from pre-commit>=2.15.0->-r requirements.txt (line 18)) (1.9.1)
Requirement already satisfied: virtualenv>=20.10.0 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from pre-commit>=2.15.0->-r requirements.txt (line 18)) (20.31.2)
Requirement already satisfied: notebook in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jupyter>=1.0.0->-r requirements.txt (line 19)) (7.3.2)
Requirement already satisfied: jupyter-console in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jupyter>=1.0.0->-r requirements.txt (line 19)) (6.6.3)
Requirement already satisfied: nbconvert in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jupyter>=1.0.0->-r requirements.txt (line 19)) (7.16.6)
Requirement already satisfied: ipywidgets in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jupyter>=1.0.0->-r requirements.txt (line 19)) (8.1.5)
Requirement already satisfied: jupyterlab in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jupyter>=1.0.0->-r requirements.txt (line 19)) (4.3.4)
Requirement already satisfied: comm>=0.1.1 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from ipykernel>=6.0.0->-r requirements.txt (line 20)) (0.2.1)
Requirement already satisfied: debugpy>=1.6.5 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from ipykernel>=6.0.0->-r requirements.txt (line 20)) (1.8.11)
Requirement already satisfied: ipython>=7.23.1 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from ipykernel>=6.0.0->-r requirements.txt (line 20)) (8.30.0)
Requirement already satisfied: jupyter-client>=6.1.12 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from ipykernel>=6.0.0->-r requirements.txt (line 20)) (8.6.3)
Requirement already satisfied: jupyter-core!=5.0.*,>=4.12 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from ipykernel>=6.0.0->-r requirements.txt (line 20)) (5.7.2)
Requirement already satisfied: matplotlib-inline>=0.1 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from ipykernel>=6.0.0->-r requirements.txt (line 20)) (0.1.6)
Requirement already satisfied: nest-asyncio in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from ipykernel>=6.0.0->-r requirements.txt (line 20)) (1.6.0)
Requirement already satisfied: psutil in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from ipykernel>=6.0.0->-r requirements.txt (line 20)) (5.9.0)
Requirement already satisfied: pyzmq>=24 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from ipykernel>=6.0.0->-r requirements.txt (line 20)) (26.2.0)
Requirement already satisfied: tornado>=6.1 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from ipykernel>=6.0.0->-r requirements.txt (line 20)) (6.5.1)
Requirement already satisfied: traitlets>=5.4.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from ipykernel>=6.0.0->-r requirements.txt (line 20)) (5.14.3)
Requirement already satisfied: patsy>=0.5.6 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from statsmodels>=0.13.0->-r requirements.txt (line 21)) (1.0.1)
Requirement already satisfied: filelock in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from torch>=1.12.0->-r requirements.txt (line 23)) (3.17.0)
Requirement already satisfied: setuptools in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from torch>=1.12.0->-r requirements.txt (line 23)) (72.1.0)
Requirement already satisfied: sympy>=1.13.3 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from torch>=1.12.0->-r requirements.txt (line 23)) (1.13.3)
Requirement already satisfied: networkx in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from torch>=1.12.0->-r requirements.txt (line 23)) (3.4.2)
Requirement already satisfied: jinja2 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from torch>=1.12.0->-r requirements.txt (line 23)) (3.1.6)
Requirement already satisfied: fsspec in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from torch>=1.12.0->-r requirements.txt (line 23)) (2025.3.2)
Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.6.77 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from torch>=1.12.0->-r requirements.txt (line 23)) (12.6.77)
Requirement already satisfied: nvidia-cuda-runtime-cu12==12.6.77 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from torch>=1.12.0->-r requirements.txt (line 23)) (12.6.77)
Requirement already satisfied: nvidia-cuda-cupti-cu12==12.6.80 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from torch>=1.12.0->-r requirements.txt (line 23)) (12.6.80)
Requirement already satisfied: nvidia-cudnn-cu12==9.5.1.17 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from torch>=1.12.0->-r requirements.txt (line 23)) (9.5.1.17)
Requirement already satisfied: nvidia-cublas-cu12==12.6.4.1 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from torch>=1.12.0->-r requirements.txt (line 23)) (12.6.4.1)
Requirement already satisfied: nvidia-cufft-cu12==11.3.0.4 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from torch>=1.12.0->-r requirements.txt (line 23)) (11.3.0.4)
Requirement already satisfied: nvidia-curand-cu12==10.3.7.77 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from torch>=1.12.0->-r requirements.txt (line 23)) (10.3.7.77)
Requirement already satisfied: nvidia-cusolver-cu12==11.7.1.2 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from torch>=1.12.0->-r requirements.txt (line 23)) (11.7.1.2)
Requirement already satisfied: nvidia-cusparse-cu12==12.5.4.2 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from torch>=1.12.0->-r requirements.txt (line 23)) (12.5.4.2)
Requirement already satisfied: nvidia-cusparselt-cu12==0.6.3 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from torch>=1.12.0->-r requirements.txt (line 23)) (0.6.3)
Requirement already satisfied: nvidia-nccl-cu12==2.26.2 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from torch>=1.12.0->-r requirements.txt (line 23)) (2.26.2)
Requirement already satisfied: nvidia-nvtx-cu12==12.6.77 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from torch>=1.12.0->-r requirements.txt (line 23)) (12.6.77)
Requirement already satisfied: nvidia-nvjitlink-cu12==12.6.85 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from torch>=1.12.0->-r requirements.txt (line 23)) (12.6.85)
Requirement already satisfied: nvidia-cufile-cu12==1.11.1.6 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from torch>=1.12.0->-r requirements.txt (line 23)) (1.11.1.6)
Requirement already satisfied: triton==3.3.1 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from torch>=1.12.0->-r requirements.txt (line 23)) (3.3.1)
Requirement already satisfied: cmdstanpy>=1.0.4 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from prophet>=1.1.0->-r requirements.txt (line 24)) (1.2.5)
Requirement already satisfied: holidays<1,>=0.25 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from prophet>=1.1.0->-r requirements.txt (line 24)) (0.76)
Requirement already satisfied: importlib_resources in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from prophet>=1.1.0->-r requirements.txt (line 24)) (6.5.2)
Requirement already satisfied: alembic>=1.5.0 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from optuna>=3.0.0->-r requirements.txt (line 25)) (1.16.2)
Requirement already satisfied: colorlog in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from optuna>=3.0.0->-r requirements.txt (line 25)) (6.9.0)
Requirement already satisfied: sqlalchemy>=1.4.2 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from optuna>=3.0.0->-r requirements.txt (line 25)) (2.0.39)
Requirement already satisfied: mlflow-skinny==3.1.1 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from mlflow>=2.0.0->-r requirements.txt (line 26)) (3.1.1)
Requirement already satisfied: Flask<4 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from mlflow>=2.0.0->-r requirements.txt (line 26)) (3.1.0)
Requirement already satisfied: docker<8,>=4.0.0 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from mlflow>=2.0.0->-r requirements.txt (line 26)) (7.1.0)
Requirement already satisfied: graphene<4 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from mlflow>=2.0.0->-r requirements.txt (line 26)) (3.4.3)
Requirement already satisfied: gunicorn<24 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from mlflow>=2.0.0->-r requirements.txt (line 26)) (23.0.0)
Requirement already satisfied: pyarrow<21,>=4.0.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from mlflow>=2.0.0->-r requirements.txt (line 26)) (19.0.0)
Requirement already satisfied: cachetools<7,>=5.0.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from mlflow-skinny==3.1.1->mlflow>=2.0.0->-r requirements.txt (line 26)) (5.5.1)
Requirement already satisfied: cloudpickle<4 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from mlflow-skinny==3.1.1->mlflow>=2.0.0->-r requirements.txt (line 26)) (3.0.0)
Requirement already satisfied: databricks-sdk<1,>=0.20.0 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from mlflow-skinny==3.1.1->mlflow>=2.0.0->-r requirements.txt (line 26)) (0.57.0)
Requirement already satisfied: fastapi<1 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from mlflow-skinny==3.1.1->mlflow>=2.0.0->-r requirements.txt (line 26)) (0.116.0)
Requirement already satisfied: gitpython<4,>=3.1.9 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from mlflow-skinny==3.1.1->mlflow>=2.0.0->-r requirements.txt (line 26)) (3.1.43)
Requirement already satisfied: importlib_metadata!=4.7.0,<9,>=3.7.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from mlflow-skinny==3.1.1->mlflow>=2.0.0->-r requirements.txt (line 26)) (8.5.0)
Requirement already satisfied: opentelemetry-api<3,>=1.9.0 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from mlflow-skinny==3.1.1->mlflow>=2.0.0->-r requirements.txt (line 26)) (1.34.1)
Requirement already satisfied: opentelemetry-sdk<3,>=1.9.0 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from mlflow-skinny==3.1.1->mlflow>=2.0.0->-r requirements.txt (line 26)) (1.34.1)
Requirement already satisfied: protobuf<7,>=3.12.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from mlflow-skinny==3.1.1->mlflow>=2.0.0->-r requirements.txt (line 26)) (5.29.3)
Requirement already satisfied: pydantic<3,>=1.10.8 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from mlflow-skinny==3.1.1->mlflow>=2.0.0->-r requirements.txt (line 26)) (2.10.3)
Requirement already satisfied: sqlparse<1,>=0.4.0 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from mlflow-skinny==3.1.1->mlflow>=2.0.0->-r requirements.txt (line 26)) (0.5.3)
Requirement already satisfied: uvicorn<1 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from mlflow-skinny==3.1.1->mlflow>=2.0.0->-r requirements.txt (line 26)) (0.35.0)
Requirement already satisfied: Mako in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from alembic>=1.5.0->optuna>=3.0.0->-r requirements.txt (line 25)) (1.3.10)
Requirement already satisfied: google-auth~=2.0 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from databricks-sdk<1,>=0.20.0->mlflow-skinny==3.1.1->mlflow>=2.0.0->-r requirements.txt (line 26)) (2.40.3)
Requirement already satisfied: starlette<0.47.0,>=0.40.0 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from fastapi<1->mlflow-skinny==3.1.1->mlflow>=2.0.0->-r requirements.txt (line 26)) (0.46.2)
Requirement already satisfied: Werkzeug>=3.1 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from Flask<4->mlflow>=2.0.0->-r requirements.txt (line 26)) (3.1.3)
Requirement already satisfied: itsdangerous>=2.2 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from Flask<4->mlflow>=2.0.0->-r requirements.txt (line 26)) (2.2.0)
Requirement already satisfied: blinker>=1.9 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from Flask<4->mlflow>=2.0.0->-r requirements.txt (line 26)) (1.9.0)
Requirement already satisfied: gitdb<5,>=4.0.1 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from gitpython<4,>=3.1.9->mlflow-skinny==3.1.1->mlflow>=2.0.0->-r requirements.txt (line 26)) (4.0.7)
Requirement already satisfied: smmap<5,>=3.0.1 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from gitdb<5,>=4.0.1->gitpython<4,>=3.1.9->mlflow-skinny==3.1.1->mlflow>=2.0.0->-r requirements.txt (line 26)) (4.0.0)
Requirement already satisfied: pyasn1-modules>=0.2.1 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.1.1->mlflow>=2.0.0->-r requirements.txt (line 26)) (0.2.8)
Requirement already satisfied: rsa<5,>=3.1.4 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.1.1->mlflow>=2.0.0->-r requirements.txt (line 26)) (4.9.1)
Requirement already satisfied: graphql-core<3.3,>=3.1 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from graphene<4->mlflow>=2.0.0->-r requirements.txt (line 26)) (3.2.6)
Requirement already satisfied: graphql-relay<3.3,>=3.1 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from graphene<4->mlflow>=2.0.0->-r requirements.txt (line 26)) (3.2.0)
Requirement already satisfied: zipp>=3.20 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from importlib_metadata!=4.7.0,<9,>=3.7.0->mlflow-skinny==3.1.1->mlflow>=2.0.0->-r requirements.txt (line 26)) (3.21.0)
Requirement already satisfied: opentelemetry-semantic-conventions==0.55b1 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from opentelemetry-sdk<3,>=1.9.0->mlflow-skinny==3.1.1->mlflow>=2.0.0->-r requirements.txt (line 26)) (0.55b1)
Requirement already satisfied: annotated-types>=0.6.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.1.1->mlflow>=2.0.0->-r requirements.txt (line 26)) (0.6.0)
Requirement already satisfied: pydantic-core==2.27.1 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.1.1->mlflow>=2.0.0->-r requirements.txt (line 26)) (2.27.1)
Requirement already satisfied: six>=1.5 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from python-dateutil>=2.8.2->pandas>=1.3.0->-r requirements.txt (line 2)) (1.17.0)
Requirement already satisfied: pyasn1>=0.1.3 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from rsa<5,>=3.1.4->google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.1.1->mlflow>=2.0.0->-r requirements.txt (line 26)) (0.4.8)
Requirement already satisfied: greenlet!=0.4.17 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from sqlalchemy>=1.4.2->optuna>=3.0.0->-r requirements.txt (line 25)) (3.1.1)
Requirement already satisfied: anyio<5,>=3.6.2 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from starlette<0.47.0,>=0.40.0->fastapi<1->mlflow-skinny==3.1.1->mlflow>=2.0.0->-r requirements.txt (line 26)) (4.7.0)
Requirement already satisfied: sniffio>=1.1 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from anyio<5,>=3.6.2->starlette<0.47.0,>=0.40.0->fastapi<1->mlflow-skinny==3.1.1->mlflow>=2.0.0->-r requirements.txt (line 26)) (1.3.0)
Requirement already satisfied: h11>=0.8 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from uvicorn<1->mlflow-skinny==3.1.1->mlflow>=2.0.0->-r requirements.txt (line 26)) (0.16.0)
Requirement already satisfied: stanio<2.0.0,>=0.4.0 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from cmdstanpy>=1.0.4->prophet>=1.1.0->-r requirements.txt (line 24)) (0.5.1)
Requirement already satisfied: decorator in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from ipython>=7.23.1->ipykernel>=6.0.0->-r requirements.txt (line 20)) (5.1.1)
Requirement already satisfied: jedi>=0.16 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from ipython>=7.23.1->ipykernel>=6.0.0->-r requirements.txt (line 20)) (0.19.2)
Requirement already satisfied: prompt-toolkit<3.1.0,>=3.0.41 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from ipython>=7.23.1->ipykernel>=6.0.0->-r requirements.txt (line 20)) (3.0.43)
Requirement already satisfied: pygments>=2.4.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from ipython>=7.23.1->ipykernel>=6.0.0->-r requirements.txt (line 20)) (2.19.1)
Requirement already satisfied: stack-data in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from ipython>=7.23.1->ipykernel>=6.0.0->-r requirements.txt (line 20)) (0.2.0)
Requirement already satisfied: pexpect>4.3 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from ipython>=7.23.1->ipykernel>=6.0.0->-r requirements.txt (line 20)) (4.8.0)
Requirement already satisfied: wcwidth in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from prompt-toolkit<3.1.0,>=3.0.41->ipython>=7.23.1->ipykernel>=6.0.0->-r requirements.txt (line 20)) (0.2.5)
Requirement already satisfied: parso<0.9.0,>=0.8.4 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jedi>=0.16->ipython>=7.23.1->ipykernel>=6.0.0->-r requirements.txt (line 20)) (0.8.4)
Requirement already satisfied: MarkupSafe>=2.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jinja2->torch>=1.12.0->-r requirements.txt (line 23)) (3.0.2)
Requirement already satisfied: ptyprocess>=0.5 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from pexpect>4.3->ipython>=7.23.1->ipykernel>=6.0.0->-r requirements.txt (line 20)) (0.7.0)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from sympy>=1.13.3->torch>=1.12.0->-r requirements.txt (line 23)) (1.3.0)
Requirement already satisfied: distlib<1,>=0.3.7 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from virtualenv>=20.10.0->pre-commit>=2.15.0->-r requirements.txt (line 18)) (0.3.9)
Requirement already satisfied: widgetsnbextension~=4.0.12 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from ipywidgets->jupyter>=1.0.0->-r requirements.txt (line 19)) (4.0.13)
Requirement already satisfied: jupyterlab-widgets~=3.0.12 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from ipywidgets->jupyter>=1.0.0->-r requirements.txt (line 19)) (3.0.13)
Requirement already satisfied: async-lru>=1.0.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (2.0.4)
Requirement already satisfied: httpx>=0.25.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (0.28.1)
Requirement already satisfied: jupyter-lsp>=2.0.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (2.2.5)
Requirement already satisfied: jupyter-server<3,>=2.4.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (2.15.0)
Requirement already satisfied: jupyterlab-server<3,>=2.27.1 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (2.27.3)
Requirement already satisfied: notebook-shim>=0.2 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (0.2.4)
Requirement already satisfied: argon2-cffi>=21.1 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jupyter-server<3,>=2.4.0->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (21.3.0)
Requirement already satisfied: jupyter-events>=0.11.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jupyter-server<3,>=2.4.0->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (0.12.0)
Requirement already satisfied: jupyter-server-terminals>=0.4.4 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jupyter-server<3,>=2.4.0->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (0.5.3)
Requirement already satisfied: nbformat>=5.3.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jupyter-server<3,>=2.4.0->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (5.10.4)
Requirement already satisfied: overrides>=5.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jupyter-server<3,>=2.4.0->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (7.4.0)
Requirement already satisfied: prometheus-client>=0.9 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jupyter-server<3,>=2.4.0->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (0.21.1)
Requirement already satisfied: send2trash>=1.8.2 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jupyter-server<3,>=2.4.0->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (1.8.2)
Requirement already satisfied: terminado>=0.8.3 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jupyter-server<3,>=2.4.0->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (0.17.1)
Requirement already satisfied: websocket-client>=1.7 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jupyter-server<3,>=2.4.0->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (1.8.0)
Requirement already satisfied: babel>=2.10 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jupyterlab-server<3,>=2.27.1->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (2.16.0)
Requirement already satisfied: json5>=0.9.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jupyterlab-server<3,>=2.27.1->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (0.9.25)
Requirement already satisfied: jsonschema>=4.18.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jupyterlab-server<3,>=2.27.1->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (4.23.0)
Requirement already satisfied: argon2-cffi-bindings in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from argon2-cffi>=21.1->jupyter-server<3,>=2.4.0->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (21.2.0)
Requirement already satisfied: httpcore==1.* in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from httpx>=0.25.0->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (1.0.9)
Requirement already satisfied: attrs>=22.2.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jsonschema>=4.18.0->jupyterlab-server<3,>=2.27.1->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (24.3.0)
Requirement already satisfied: jsonschema-specifications>=2023.03.6 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jsonschema>=4.18.0->jupyterlab-server<3,>=2.27.1->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (2023.7.1)
Requirement already satisfied: referencing>=0.28.4 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jsonschema>=4.18.0->jupyterlab-server<3,>=2.27.1->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (0.30.2)
Requirement already satisfied: rpds-py>=0.7.1 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jsonschema>=4.18.0->jupyterlab-server<3,>=2.27.1->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (0.22.3)
Requirement already satisfied: python-json-logger>=2.0.4 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jupyter-events>=0.11.0->jupyter-server<3,>=2.4.0->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (3.2.1)
Requirement already satisfied: rfc3339-validator in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jupyter-events>=0.11.0->jupyter-server<3,>=2.4.0->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (0.1.4)
Requirement already satisfied: rfc3986-validator>=0.1.1 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jupyter-events>=0.11.0->jupyter-server<3,>=2.4.0->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (0.1.1)
Requirement already satisfied: fqdn in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from jsonschema[format-nongpl]>=4.18.0->jupyter-events>=0.11.0->jupyter-server<3,>=2.4.0->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (1.5.1)
Requirement already satisfied: isoduration in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from jsonschema[format-nongpl]>=4.18.0->jupyter-events>=0.11.0->jupyter-server<3,>=2.4.0->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (20.11.0)
Requirement already satisfied: jsonpointer>1.13 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from jsonschema[format-nongpl]>=4.18.0->jupyter-events>=0.11.0->jupyter-server<3,>=2.4.0->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (2.1)
Requirement already satisfied: uri-template in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from jsonschema[format-nongpl]>=4.18.0->jupyter-events>=0.11.0->jupyter-server<3,>=2.4.0->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (1.3.0)
Requirement already satisfied: webcolors>=24.6.0 in /zhome/bb/9/101964/.local/lib/python3.13/site-packages (from jsonschema[format-nongpl]>=4.18.0->jupyter-events>=0.11.0->jupyter-server<3,>=2.4.0->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (24.11.1)
Requirement already satisfied: beautifulsoup4 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from nbconvert->jupyter>=1.0.0->-r requirements.txt (line 19)) (4.12.3)
Requirement already satisfied: bleach!=5.0.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from bleach[css]!=5.0.0->nbconvert->jupyter>=1.0.0->-r requirements.txt (line 19)) (6.2.0)
Requirement already satisfied: defusedxml in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from nbconvert->jupyter>=1.0.0->-r requirements.txt (line 19)) (0.7.1)
Requirement already satisfied: jupyterlab-pygments in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from nbconvert->jupyter>=1.0.0->-r requirements.txt (line 19)) (0.3.0)
Requirement already satisfied: mistune<4,>=2.0.3 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from nbconvert->jupyter>=1.0.0->-r requirements.txt (line 19)) (3.1.2)
Requirement already satisfied: nbclient>=0.5.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from nbconvert->jupyter>=1.0.0->-r requirements.txt (line 19)) (0.10.2)
Requirement already satisfied: pandocfilters>=1.4.1 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from nbconvert->jupyter>=1.0.0->-r requirements.txt (line 19)) (1.5.0)
Requirement already satisfied: webencodings in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from bleach!=5.0.0->bleach[css]!=5.0.0->nbconvert->jupyter>=1.0.0->-r requirements.txt (line 19)) (0.5.1)
Requirement already satisfied: tinycss2<1.5,>=1.1.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from bleach[css]!=5.0.0->nbconvert->jupyter>=1.0.0->-r requirements.txt (line 19)) (1.4.0)
Requirement already satisfied: fastjsonschema>=2.15 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from nbformat>=5.3.0->jupyter-server<3,>=2.4.0->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (2.20.0)
Requirement already satisfied: cffi>=1.0.1 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from argon2-cffi-bindings->argon2-cffi>=21.1->jupyter-server<3,>=2.4.0->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (1.17.1)
Requirement already satisfied: pycparser in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from cffi>=1.0.1->argon2-cffi-bindings->argon2-cffi>=21.1->jupyter-server<3,>=2.4.0->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (2.21)
Requirement already satisfied: soupsieve>1.2 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from beautifulsoup4->nbconvert->jupyter>=1.0.0->-r requirements.txt (line 19)) (2.5)
Requirement already satisfied: arrow>=0.15.0 in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from isoduration->jsonschema[format-nongpl]>=4.18.0->jupyter-events>=0.11.0->jupyter-server<3,>=2.4.0->jupyterlab->jupyter>=1.0.0->-r requirements.txt (line 19)) (1.3.0)
Requirement already satisfied: executing in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from stack-data->ipython>=7.23.1->ipykernel>=6.0.0->-r requirements.txt (line 20)) (0.8.3)
Requirement already satisfied: asttokens in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from stack-data->ipython>=7.23.1->ipykernel>=6.0.0->-r requirements.txt (line 20)) (3.0.0)
Requirement already satisfied: pure-eval in /work3/xiuli/anaconda3/lib/python3.13/site-packages (from stack-data->ipython>=7.23.1->ipykernel>=6.0.0->-r requirements.txt (line 20)) (0.2.2)
=== Running comprehensive evaluation ===
2025-07-10 11:30:23,274 - comprehensive_evaluation - INFO - Starting comprehensive evaluation for TNNLS paper
2025-07-10 11:30:23,275 - comprehensive_evaluation - INFO - Configuration: benchmark_config
2025-07-10 11:30:23,276 - comprehensive_evaluation - INFO - Datasets: ['gefcom2014_energy', 'kaggle_solar_plant', 'kaggle_wind_power', 'nrel_solar', 'uk_sheffield_solar', 'entso_e_solar']
2025-07-10 11:30:23,277 - comprehensive_evaluation - INFO - Output directory: results/comprehensive
2025-07-10 11:30:23,301 - comprehensive_evaluation - INFO - Running main comparison experiments
2025-07-10 11:49:51,487 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 11:49:51,491 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 11:49:51,493 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 11:49:51,509 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 11:57:44,858 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 11:58:32,395 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 11:58:32,396 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 11:58:32,396 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 11:58:32,399 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 11:58:44,433 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 12:02:35,453 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 12:02:35,454 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 12:02:35,454 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 12:02:35,456 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 12:03:37,898 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 12:10:29,658 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 12:10:29,658 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 12:10:29,659 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 12:10:29,663 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 12:12:26,770 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 12:15:20,450 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 12:15:20,451 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 12:15:20,452 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 12:15:20,456 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 12:16:34,953 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 12:26:49,398 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 12:26:49,399 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 12:26:49,399 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 12:26:49,404 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 12:29:36,625 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 12:29:36,627 - comprehensive_evaluation - INFO - Running ablation studies
2025-07-10 12:29:36,679 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 12:29:36,680 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 12:29:36,680 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 12:29:36,684 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 12:32:22,405 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 12:32:22,408 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 12:32:22,408 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 12:32:22,409 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 12:32:22,413 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 12:35:08,292 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 12:35:08,295 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 12:35:08,296 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 12:35:08,296 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 12:35:08,301 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 12:37:54,109 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 12:37:54,112 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 12:37:54,112 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 12:37:54,113 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 12:37:54,117 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 12:40:39,651 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 12:40:39,653 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 12:40:39,653 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 12:40:39,654 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 12:40:39,658 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 12:43:25,453 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 12:43:25,455 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 12:43:25,456 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 12:43:25,456 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 12:43:25,461 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 12:46:11,073 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 12:46:12,199 - comprehensive_evaluation - INFO - Running sensitivity analysis
2025-07-10 12:46:12,249 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 12:46:12,249 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 12:46:12,250 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 12:46:12,253 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 12:46:49,142 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 12:46:49,145 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 12:46:49,145 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 12:46:49,145 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 12:46:49,150 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 12:48:28,587 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 12:48:28,589 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 12:48:28,590 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 12:48:28,590 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 12:48:28,595 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 12:51:14,135 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 12:51:14,140 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 12:51:14,140 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 12:51:14,142 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 12:51:14,147 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 12:57:01,226 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 12:57:01,297 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 12:57:01,297 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 12:57:01,298 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 12:57:01,308 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 13:06:26,866 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 13:06:26,872 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 13:06:26,874 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 13:06:26,877 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 13:06:26,883 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 13:22:25,323 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 13:22:25,327 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 13:22:25,328 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 13:22:25,329 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 13:22:25,335 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 13:36:55,211 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 13:36:55,214 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 13:36:55,215 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 13:36:55,216 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 13:36:55,221 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 13:52:52,881 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 13:52:52,883 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 13:52:52,884 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 13:52:52,884 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 13:52:52,889 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 14:09:47,173 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 14:09:47,176 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 14:09:47,177 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 14:09:47,178 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 14:09:47,183 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 14:28:28,894 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 14:28:28,897 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 14:28:28,898 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 14:28:28,898 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 14:28:28,903 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 14:47:09,542 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 14:47:09,544 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 14:47:09,545 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 14:47:09,545 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 14:47:09,550 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 15:05:49,699 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 15:05:49,702 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 15:05:49,703 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 15:05:49,704 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 15:05:49,709 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 15:24:33,892 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 15:24:33,895 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 15:24:33,895 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 15:24:33,896 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 15:24:33,901 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 15:43:16,990 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 15:43:16,993 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 15:43:16,993 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 15:43:16,994 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 15:43:16,999 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 16:01:58,358 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 16:01:58,361 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 16:01:58,362 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 16:01:58,362 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 16:01:58,367 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 16:20:37,586 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 16:20:37,589 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 16:20:37,589 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 16:20:37,589 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 16:20:37,594 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 16:39:20,196 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 16:39:20,199 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 16:39:20,199 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 16:39:20,200 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 16:39:20,204 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 16:57:58,851 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 16:57:58,854 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 16:57:58,855 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 16:57:58,856 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 16:57:58,861 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 17:16:42,309 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 17:16:42,312 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 17:16:42,313 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 17:16:42,313 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 17:16:42,318 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 17:35:23,754 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 17:35:23,757 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 17:35:23,758 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 17:35:23,759 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 17:35:23,764 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 17:54:05,175 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 17:54:05,178 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 17:54:05,179 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 17:54:05,180 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 17:54:05,185 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 18:12:47,366 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 18:12:47,369 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 18:12:47,371 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 18:12:47,371 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 18:12:47,377 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 18:31:28,778 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 18:31:28,781 - comprehensive_evaluation - INFO - Running computational analysis
2025-07-10 18:31:28,857 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 18:31:28,858 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 18:31:28,859 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 18:31:28,862 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 18:32:08,873 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 18:32:14,107 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 18:32:14,108 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 18:32:14,109 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 18:32:14,111 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 18:34:51,464 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 18:35:06,840 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 18:35:06,841 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 18:35:06,842 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 18:35:06,846 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 18:41:17,133 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 18:42:04,230 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 18:42:04,231 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 18:42:04,231 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 18:42:04,235 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering
2025-07-10 18:53:56,215 - NeutrosophicForecastingFramework - INFO - Stage 3: Neutrosophic transformation
2025-07-10 18:55:43,050 - comprehensive_evaluation - INFO - Running cross-dataset evaluation
2025-07-10 18:55:43,300 - NeutrosophicForecastingFramework - INFO - Neutrosophic Forecasting Framework initialized
2025-07-10 18:55:43,300 - NeutrosophicForecastingFramework - INFO - Starting framework training
2025-07-10 18:55:43,301 - NeutrosophicForecastingFramework - INFO - Stage 1: Data preprocessing
2025-07-10 18:55:43,308 - NeutrosophicForecastingFramework - INFO - Stage 2: Dual clustering

------------------------------------------------------------
Sender: LSF System <<EMAIL>>
Subject: Job 25543173: <dual_cluster_comprehensive> in cluster <dcc> Exited

Job <dual_cluster_comprehensive> was submitted from host <hpclogin2> by user <xiuli> in cluster <dcc> at Thu Jul 10 11:29:17 2025
Job was executed on host(s) <8*n-62-18-13>, in queue <gpua100>, as user <xiuli> in cluster <dcc> at Thu Jul 10 11:29:17 2025
</zhome/bb/9/101964> was used as the home directory.
</zhome/bb/9/101964/xiuli/dual_clustering> was used as the working directory.
Started at Thu Jul 10 11:29:17 2025
Terminated at Thu Jul 10 19:29:18 2025
Results reported at Thu Jul 10 19:29:18 2025

Your job looked like:

------------------------------------------------------------
# LSBATCH: User input
#!/bin/bash
### Comprehensive evaluation script for dual clustering experiments
#BSUB -q gpua100                   # Queue name (choose based on GPU type)
#BSUB -J dual_cluster_comprehensive # Job name
#BSUB -n 8                         # Number of CPU cores
#BSUB -gpu "num=1:mode=exclusive_process"  # One GPU in exclusive mode
#BSUB -R "rusage[mem=16GB]"        # 16 GB system memory
#BSUB -W 8:00                      # Walltime: 8 hours
#BSUB -o gpu_comprehensive_%J.out  # Output file
#BSUB -e gpu_comprehensive_%J.err  # Error file

# Source bashrc to set up local Python environment
source ~/.bashrc

# Load only CUDA module (using local Python)
module load cuda/12.6

# Set environment variables
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
export CUDA_VISIBLE_DEVICES=0
export OMP_NUM_THREADS=8

# Change to project directory
cd /zhome/bb/9/101964/xiuli/dual_clustering

echo "=== Starting Comprehensive Evaluation ==="
echo "Job ID: $LSB_JOBID"
echo "Host: $(hostname)"
echo "Date: $(date)"
echo "Working directory: $(pwd)"

# Install dependencies
echo "=== Installing dependencies ==="
pip install --user -r requirements.txt

# Run comprehensive evaluation
echo "=== Running comprehensive evaluation ==="

# Run full comprehensive evaluation with all components
python experiments/comprehensive_evaluation.py --config benchmark_config

# Alternative: Run specific components only (uncomment if needed)
# python experiments/comprehensive_evaluation.py \
#     --config benchmark_config \
#     --datasets entso_e_solar entso_e_wind gefcom2014_solar \
#     --skip-computational --skip-cross-dataset --skip-robustness

echo "=== Comprehensive evaluation completed ==="
echo "Results saved in: results/comprehensive/"

------------------------------------------------------------

TERM_RUNLIMIT: job killed after reaching LSF run time limit.
Exited with exit code 140.

Resource usage summary:

    CPU time :                                   41555.00 sec.
    Max Memory :                                 15176 MB
    Average Memory :                             2375.20 MB
    Total Requested Memory :                     131072.00 MB
    Delta Memory :                               115896.00 MB
    Max Swap :                                   -
    Max Processes :                              5
    Max Threads :                                35
    Run time :                                   28811 sec.
    Turnaround time :                            28801 sec.

The output (if any) is above this job summary.



PS:

Read file <gpu_comprehensive_25543173.err> for stderr output of this job.

